@use "sass:map";

* {
  border: none;
  outline: none;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  -webkit-overflow-scrolling: touch;
  // overflow-y: hidden;
}

#App {
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
}

$sizes: 8, 12, 13, 14, 15, 16, 18, 20, 22, 24, 25, 26, 28, 30, 32, 34, 35, 36,
  38, 40, 42, 44, 45, 46, 48, 50, 54, 60;

@each $size in $sizes {
  .fs-#{$size} {
    font-size: rem($size * 1rem);
    // line-height: rem($size * 1rem);
  }
}

@each $x
  in -100 -80 -50 -44 -40 -30 -20 -10 -5 -3 -2
  0
  1
  2
  3
  4
  5
  10
  12
  15
  20
  22
  25
  28
  30
  34
  40
  44
  45
  48
  50
  60
  70
  75
  80
  88
  90
  100
  120
  130
  140
{
  .pd-#{$x} {
    padding: rem($x * 1rem);
  }

  .mg-#{$x} {
    margin: rem($x * 1rem);
  }

  .mgt-#{$x} {
    margin-top: rem($x * 1rem);
  }

  .mgb-#{$x} {
    margin-bottom: rem($x * 1rem);
  }

  .mgl-#{$x} {
    margin-left: rem($x * 1rem);
  }

  .mgr-#{$x} {
    margin-right: rem($x * 1rem);
  }

  .pd-#{$x} {
    padding: rem($x * 1rem);
  }

  .pdt-#{$x} {
    padding-top: rem($x * 1rem);
  }

  .pdb-#{$x} {
    padding-bottom: rem($x * 1rem);
  }

  .pdl-#{$x} {
    padding-left: rem($x * 1rem);
  }

  .pdr-#{$x} {
    padding-right: rem($x * 1rem);
  }

  .pol-#{$x} {
    left: rem($x * 1rem);
  }

  .por-#{$x} {
    right: rem($x * 1rem);
  }

  .pot-#{$x} {
    top: rem($x * 1rem);
  }

  .pob-#{$x} {
    bottom: rem($x * 1rem);
  }

  .h-#{$x} {
    height: rem($x * 1rem);
  }

  .w-#{$x} {
    width: rem($x * 1rem);
  }

  .lh-#{$x} {
    line-height: rem($x * 1rem);
  }

  .hlh-#{$x} {
    height: rem($x * 1rem);
    line-height: rem($x * 1rem);
  }
}

.fw-b {
  font-weight: bold;
}

.flex-1 {
  flex: 1;
}

.flex {
  display: flex;
}

.flex-middle {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-middle-only {
  display: flex;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.fdr-r {
  flex-direction: row;
}

.fdr-c {
  flex-direction: column;
}

.fw-w {
  flex-wrap: wrap;
}

.jc-c {
  justify-content: center;
}

.jc-s {
  justify-content: start;
}

.jc-e {
  justify-content: flex-end;
}

.jc-sb {
  justify-content: space-between;
}

.jc-sa {
  justify-content: space-around;
}

.ai-c {
  align-items: center;
}

.ai-s {
  align-items: stretch;
}

.ai-fe {
  align-items: flex-end;
}

.hide {
  display: none;
}

.ovhide {
  overflow: hidden;
}

.ovscroll {
  overflow: scroll;
}

.flt-r {
  float: right;
}

.flt-l {
  float: left;
}

.fg-1 {
  flex-grow: 1;
}

.flex1 {
  flex: 1;
}

.block {
  display: block;
}

.inlb {
  display: inline-block;
}

.va-m {
  vertical-align: middle;
}

.va-t {
  vertical-align: top;
}

.pos-r {
  position: relative;
}

.pos-a {
  position: absolute;
}

.pos-f {
  position: fixed;
}

.ab-middle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}

.ab-middle-y {
  position: absolute !important;
  top: 50%;
  transform: translateY(-50%);
}

.txt-c {
  text-align: center;
}

.txt-l {
  text-align: left;
}

.txt-r {
  text-align: right;
}

.txt-j {
  text-align: justify;
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

.nobd {
  border: none;
}

.pos-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}

.crs-p {
  cursor: pointer;
}

.clear {
  &::after {
    content: "";
    clear: both;
    display: block;
  }
}

.txt-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: keep-all;
}

.white {
  color: #fff;
}

.black {
  color: #000;
}

.bg-white {
  background-color: #fff;
}

.gray {
  color: #afb0ba;
}

.cles-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  //display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
}

.cles-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  //display: -webkit-box;
  -webkit-line-clamp: 2;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
}

.fs10 {
  display: inline-block;
  font-size: 12px !important;
  transform: scale(0.67);
  white-space: normal;

  &.origin-center {
    transform-origin: center;
  }

  &.origin-left {
    transform-origin: left;
  }

  &.origin-right {
    transform-origin: right;
  }
}

.fs12 {
  display: inline-block;
  font-size: 12px !important;
  transform: scale(0.75);

  //white-space: normal;
  &.origin-center {
    transform-origin: center;
  }

  &.origin-left {
    transform-origin: left;
  }

  &.origin-right {
    transform-origin: right;
  }
}

.nav-service {
  position: absolute;
  right: rem(40rem);
  top: rem(24rem);
  display: inline-block;
  width: rem(32rem);
  height: rem(32rem);
}

input:-internal-autofill-selected {
  appearance: menulist-button;
  background-image: none !important;
  background-color: transparent !important;
  //color: -internal-light-dark(black, white) !important;
}

.base-shadow {
  box-shadow: 0 rem(8rem) rem(26rem) 0 rgba(0, 0, 0, 0.09);
}

.radius-8 {
  border-radius: rem(16rem);
}

.scroll-y {
  overflow-y: scroll;
}

.base-bd-radius {
  border-radius: rem(16rem);
}

.under-line {
  border-bottom: 1px solid #f2f2f2;
}

.p-wp {
  height: 100%;
  overflow: scroll;
  background-position: center 0rem;
}

.init-img {
  width: 100%;
  opacity: 0;
  transition: all 0.4s ease-in-out;
}

.thumb-loaded {
  width: 100%;
  opacity: 1;
  filter: blur(10px);
  transform: scale(1);
}

// 自定义公共样式
.cg-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
