import { createApp, defineAsyncComponent, Directive } from 'vue';
import { createI18n } from 'vue-i18n';

import router from './router';
import { key, store } from './store';
import storePinia from './store-pinia';
import Analytics from '@168bj4/analytics-mtm';
import {
  ActionSheet,
  Button,
  Checkbox,
  Circle,
  Col,
  Collapse,
  CollapseItem,
  DatetimePicker,
  Field,
  Icon,
  List,
  NavBar,
  NoticeBar,
  Overlay,
  Picker,
  Popup,
  PullRefresh,
  Row,
  Swipe,
  SwipeItem,
  Tab,
  Tabs,
  Toast,
} from 'vant';

import AppImage from '@/components/app-image/index.vue';
import * as directives from '@/directives';
import VueI18n from '@/language/index';

import 'vant/lib/index.css';

// 先导入基础变量
import '@/theme/cg-template-2/white-blue/variables.scss';

import { themeManager } from './utils/themeManager';

const messages = require(`@/language/${globalConfig.LANG}.js`);

const App = defineAsyncComponent(
  () => import(`@/components/${globalConfig.TEMPLATE}/App.vue`),
);

const i18n: any = createI18n({
  legacy: false,
  locale: globalConfig.LANG,
  fallbackLocale: globalConfig.LANG,
  messages: {
    [globalConfig.LANG]: {
      ...messages,
    },
  },
});

Toast.setDefaultOptions({ duration: 2000, className: 'cg-toast' });

const app = createApp(App);
app.config.globalProperties.site = globalConfig.SITE;
app.config.globalProperties.siteName = globalConfig.SITE_NAME;
app.config.globalProperties.$$t = i18n.global.t;

// 注册自定义指令
// tslint:disable-next-line: no-shadowed-variable
Object.keys(directives).forEach((key) => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});
app.component('AppImage', AppImage);

if (globalConfig.STORE === 'vuex') {
  app.use(store, key);
} else {
  app.use(storePinia);
}

interface JsVar {
  siteId: string;
  siteType: string;
  siteLocale: string;
  siteAnalyticsUrl: string;
  siteAnalyticsId: string; // 可以根據實際類型進行調整
}

const _jsvar: JsVar = {
  siteId: 'p38',
  siteType: 'p38',
  siteLocale: 'vn_vn',
  siteAnalyticsUrl: globalConfig.ANALYTICS.url,
  siteAnalyticsId: globalConfig.ANALYTICS.id,
};

window._jsvar = _jsvar;
// @ts-ignore
window.$t = i18n.global.t;

themeManager.initTheme();

// app.use(Analytics, {
//   url: globalConfig.ANALYTICS.url,
//   id: globalConfig.ANALYTICS.id,
// });

// const options: any = { router };
// Analytics.bind(options);

app
  .use(router)
  .use(Icon)
  .use(NavBar)
  .use(List)
  .use(Popup)
  .use(Picker)
  .use(PullRefresh)
  .use(DatetimePicker)
  .use(Overlay)
  .use(Tab)
  .use(Tabs)
  .use(Col)
  .use(Row)
  .use(Field)
  .use(NoticeBar)
  .use(Swipe)
  .use(SwipeItem)
  .use(Checkbox)
  .use(Button)
  .use(ActionSheet)
  .use(Circle)
  .use(Collapse)
  .use(CollapseItem)
  .use(VueI18n)
  .mount('#App');

if ((module as any).hot) {
  (module as any).hot.accept();
}

// 禁止缩放
document.addEventListener('gesturestart', (event) => {
  event.preventDefault();
});

function showToastForOC(msg: string) {
  Toast({
    message: msg,
  });
}
window.showToastForOC = showToastForOC;
